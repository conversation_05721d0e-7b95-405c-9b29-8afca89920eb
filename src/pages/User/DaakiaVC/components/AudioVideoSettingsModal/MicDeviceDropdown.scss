.mic-device-dropdown {
  .ant-dropdown-menu {
    background: #FFFFFF;
    border-radius: 8px !important;
    min-width: 250px;
    max-width: 90vw;
    padding: 4px;
    border: 1px solid #E3E3E3;
    margin-top: 8px;

    .ant-dropdown-menu-item {
      padding: 0;
      margin: 2px 0;
      width: 100%;

      &:hover {
        background: #F5F5F5;
      }
    }
  }
}

.mic-device-item {
  display: flex;
  align-items: center;
  padding: 6px;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
  height: 100%;

  &.selected {
    background-color: #E3E3E3;
    padding: 6px;
  }

  .device-name {
    font-size: 13px;
    color: #555454;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.dropdown-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;

  .dropdown-icon {
    color: #555454;
  }
}

.mic-device-dropdown-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px;

  .mic-device-icon {
    width: 20px;
    height: 20px;
    background: rgb(59, 96, 228);
    background: linear-gradient(
      90deg,
      rgba(59, 96, 228, 1) 0%,
      rgba(86, 123, 255, 1) 100%
    );
    padding: 4px;
  }

  .mic-device-menu {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #a8a8a8;
    border-radius: 3px;
    padding: 0.4rem 0.8rem;
    font-size: 13px;
    color: #555454;
    font-weight: 500;
    background-color: #fff;

    .mic-device-select {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 6px;
    }

    .lk-device-menu {
      background-color: #fff;
      width: 100%;
      left: 0 !important;
      top: 2.5rem !important;
      border: 1px solid #a8a8a8;
      border-radius: 3px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      .lk-media-device-select {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          padding: 6px 10px;
          cursor: pointer;
          font-weight: 500;
          color: #555454;
          font-size: 13px;

          &:hover {
            background-color: #f4f8f9;
          }
        }
      }
    }
  }
} 