import React, { useState, useEffect, useRef } from "react";
import { Mo<PERSON>, Tabs, Select, Slider, Switch, Progress, Dropdown } from "antd";
import { CloseOutlined, DownOutlined } from "@ant-design/icons";
import { PiVideoCameraFill } from "react-icons/pi";
import { ReactComponent as VoiceIcon } from "../settings/icons/VoiceIco.svg";
import { ReactComponent as CameraIcon } from "./Assets/cameraDevice.svg";
import { ReactComponent as NoiseCancellationIcon } from "./Assets/noiseCancellation.svg";
import { ReactComponent as EchoCancellationIcon } from "./Assets/echoCancellation.svg";
import { ReactComponent as MirrorSelfIcon } from "./Assets/mirrorSelf.svg";
import "./SettingsPrejoin.scss";

const { Option } = Select;

export default function SettingsPrejoin({
  open,
  setOpen,
  // Audio props
  audioDeviceId,
  setAudioDeviceId,
  audioTrack,
  // Video props
  videoDeviceId,
  setVideoDeviceId,
  // Speaker props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Settings props
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness = 100,
  onBrightnessChange,
  // Permission props
  permissions = { camera: false, microphone: false },
}) {
  // Device states
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);

  // Audio level states
  const [outputLevel, setOutputLevel] = useState(0);
  const [inputVolume, setInputVolume] = useState(100);
  const [outputVolume, setOutputVolume] = useState(100);

  // Test mic states
  const [isMicTesting, setIsMicTesting] = useState(false);
  const [testMicLevel, setTestMicLevel] = useState(0);

  // Audio settings states
  const [noiseCancellation, setNoiseCancellation] = useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression || false
  );
  const [echoCancellation, setEchoCancellation] = useState(
    room?.options?.audioCaptureDefaults?.echoCancellation || false
  );
  const [autoMuteOnJoin, setAutoMuteOnJoin] = useState(true);

  // Audio level monitoring
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Test microphone monitoring
  const testAudioContextRef = useRef(null);
  const testAnalyserRef = useRef(null);
  const testAnimationFrameRef = useRef(null);
  const testStreamRef = useRef(null);

  // Scrollbar visibility refs
  const scrollTimeoutRef = useRef(null);

  // Fetch available devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

        setAudioDevices(audioInputs);
        setVideoDevices(videoInputs);
        setSpeakerDevices(audioOutputs);
      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    };

    if (open && (permissions.camera || permissions.microphone)) {
      fetchDevices();
    }
  }, [open, permissions]);

  // Audio level monitoring setup
  useEffect(() => {
    if (!audioTrack || !open) return;

    const setupAudioMonitoring = async () => {
      try {
        const stream = audioTrack.mediaStream;
        if (!stream) return;

        audioContextRef.current = new AudioContext();
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();

        analyserRef.current.fftSize = 256;
        source.connect(analyserRef.current);

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

        const updateLevel = () => {
          if (!analyserRef.current) return;

          analyserRef.current.getByteFrequencyData(dataArray);
          // const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
          // const level = Math.min(100, (average / 255) * 100);
          // Input level monitoring removed as it's not used

          animationFrameRef.current = requestAnimationFrame(updateLevel);
        };

        updateLevel();
      } catch (error) {
        console.error('Error setting up audio monitoring:', error);
      }
    };

    setupAudioMonitoring();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [audioTrack, open]);

  // Handle scrollbar visibility
  useEffect(() => {
    const handleScroll = (event) => {
      const element = event.target;

      // Add scrolling class
      element.classList.add('scrolling');

      // Clear existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Remove scrolling class after scroll ends
      scrollTimeoutRef.current = setTimeout(() => {
        element.classList.remove('scrolling');
      }, 1000); // Hide scrollbar 1 second after scrolling stops
    };

    if (open) {
      // Add scroll listeners to all scrollable elements
      const scrollableElements = document.querySelectorAll('.ant-tabs-tabpane, .settings-content');

      scrollableElements.forEach(element => {
        element.addEventListener('scroll', handleScroll, { passive: true });
      });

      return () => {
        scrollableElements.forEach(element => {
          element.removeEventListener('scroll', handleScroll);
        });

        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [open]);

  // Test microphone functions
  const stopMicrophoneTest = () => {
    setIsMicTesting(false);
    setTestMicLevel(0);

    // Stop animation frame
    if (testAnimationFrameRef.current) {
      cancelAnimationFrame(testAnimationFrameRef.current);
    }

    // Close audio context
    if (testAudioContextRef.current) {
      testAudioContextRef.current.close();
    }

    // Stop media stream
    if (testStreamRef.current) {
      testStreamRef.current.getTracks().forEach(track => track.stop());
    }
  };

  const startMicrophoneTest = async () => {
    try {
      setIsMicTesting(true);

      // Get microphone stream
      const constraints = {
        audio: audioDeviceId ? { deviceId: audioDeviceId } : true,
        video: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      testStreamRef.current = stream;

      // Setup audio context and analyser
      testAudioContextRef.current = new AudioContext();
      const source = testAudioContextRef.current.createMediaStreamSource(stream);
      testAnalyserRef.current = testAudioContextRef.current.createAnalyser();

      // Optimized settings for better audio level detection
      testAnalyserRef.current.fftSize = 2048; // Higher resolution for better accuracy
      testAnalyserRef.current.smoothingTimeConstant = 0.1; // Less smoothing for more responsive detection
      source.connect(testAnalyserRef.current);

      const dataArray = new Uint8Array(testAnalyserRef.current.frequencyBinCount);

      const updateTestLevel = () => {
        if (!testAnalyserRef.current) return;

        // Get time domain data for RMS calculation
        testAnalyserRef.current.getByteTimeDomainData(dataArray);

        // Calculate RMS (Root Mean Square) for audio level
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          const sample = (dataArray[i] - 128) / 128; // Convert to -1 to 1 range
          sum += sample * sample;
        }
        const rms = Math.sqrt(sum / dataArray.length);

        // Convert to percentage and amplify for better visibility
        let level = rms * 100 * 5; // Amplify by 5x for better sensitivity
        level = Math.min(100, level); // Cap at 100%

        console.log('Audio level:', level.toFixed(2), 'RMS:', rms.toFixed(4));
        setTestMicLevel(level);

        if (isMicTesting) {
          testAnimationFrameRef.current = requestAnimationFrame(updateTestLevel);
        }
      };

      updateTestLevel();

      // Auto-stop after 10 seconds
      setTimeout(() => {
        if (isMicTesting) {
          stopMicrophoneTest();
        }
      }, 10000);

    } catch (error) {
      console.error('Error starting microphone test:', error);
      setIsMicTesting(false);
    }
  };

  // Cleanup microphone test when modal closes
  useEffect(() => {
    if (!open && isMicTesting) {
      stopMicrophoneTest();
    }
  }, [open, isMicTesting]);

  // Handle device changes
  const handleAudioDeviceChange = (deviceId) => {
    setAudioDeviceId(deviceId);
  };

  const handleVideoDeviceChange = (deviceId) => {
    setVideoDeviceId(deviceId);
  };

  const handleSpeakerDeviceChange = (deviceId) => {
    if (setSpeakerDeviceId) {
      setSpeakerDeviceId(deviceId);
    }
  };

  // Handle audio settings changes
  const handleNoiseCancellation = (checked) => {
    setNoiseCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.noiseSuppression = checked;
    }
  };

  const handleEchoCancellation = (checked) => {
    setEchoCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.echoCancellation = checked;
    }
  };

  // Test functions
  const testMicrophone = async () => {
    if (isMicTesting) {
      // Stop testing
      stopMicrophoneTest();
    } else {
      // Start testing
      await startMicrophoneTest();
    }
  };

  const testSpeaker = () => {
    // Play a test sound or trigger output level for visual feedback
    setOutputLevel(80);
    setTimeout(() => setOutputLevel(0), 1000);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const truncateDeviceName = (name, maxLength = 30) => {
    return name.length > maxLength ? `${name.slice(0, maxLength)}...` : name;
  };

  // Create dropdown items for microphone devices
  const createMicrophoneDropdownItems = () => {
    if (!permissions.microphone) {
      return [{
        key: 'no-permission',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">Grant microphone permission to see devices</span>
          </div>
        ),
        disabled: true
      }];
    }

    return audioDevices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`device-dropdown-item ${device.deviceId === audioDeviceId ? 'selected' : ''}`}
          onClick={() => handleAudioDeviceChange(device.deviceId)}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || 'Default Microphone')}
          </span>
        </div>
      ),
    }));
  };

  // Create dropdown items for speaker devices
  const createSpeakerDropdownItems = () => {
    if (!permissions.microphone) {
      return [{
        key: 'no-permission',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">Grant microphone permission to see devices</span>
          </div>
        ),
        disabled: true
      }];
    }

    return speakerDevices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`device-dropdown-item ${device.deviceId === speakerDeviceId ? 'selected' : ''}`}
          onClick={() => handleSpeakerDeviceChange(device.deviceId)}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || 'Default Speaker')}
          </span>
        </div>
      ),
    }));
  };

  // Get selected device name
  const getSelectedMicrophoneName = () => {
    const selectedDevice = audioDevices.find(device => device.deviceId === audioDeviceId);
    return truncateDeviceName(selectedDevice?.label || 'Select microphone');
  };

  const getSelectedSpeakerName = () => {
    const selectedDevice = speakerDevices.find(device => device.deviceId === speakerDeviceId);
    return truncateDeviceName(selectedDevice?.label || 'Select speaker');
  };

  // Audio Level Blocks Component
  function AudioLevelBlocks({ level, isActive = false }) {
    const numberOfBlocks = 10;
    const activeBlocks = Math.ceil((level / 100) * numberOfBlocks);

    return (
      <div className="audio-level-blocks">
        {Array.from({ length: numberOfBlocks }, (_, index) => (
          <div
            key={index}
            className={`audio-block ${
              isActive && index < activeBlocks ? 'active' : ''
            }`}
          />
        ))}
      </div>
    );
  }

  // Tab items configuration
  const tabItems = [
    {
      key: 'audio',
      label: (
        <div className="tab-label">
          <VoiceIcon />
          <span>Audio</span>
        </div>
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <div className="settings-header">
              <h3>Audio Settings</h3>
              <p className="settings-description">Change your audio settings here</p>
            </div>
          </div>

          {/* Microphone Section */}
          <div className="settings-section">
            <div className="setting-row three-column microphone-section">
              <div className="setting-label-col">
                <span className="setting-label">Microphone</span>
              </div>
              <div className="setting-description-col">
                <div className="setting-item">
                  <span className="setting-sublabel">Choose Microphone</span>
                </div>
                <div className="setting-item">
                  <span className="setting-sublabel">Test Mic</span>
                </div>
                <div className="setting-item">
                  <span className="setting-sublabel">Input Level:</span>
                </div>
                <div className="setting-item">
                  <span className="setting-sublabel">Input Volume:</span>
                </div>
              </div>
              <div className="setting-control-col">
                <div className="setting-item">
                  <Dropdown
                    menu={{ items: createMicrophoneDropdownItems() }}
                    trigger={['click']}
                    placement="bottomLeft"
                    overlayClassName="device-settings-dropdown"
                    disabled={!permissions.microphone}
                  >
                    <div className="custom-device-select">
                      <span className="device-select-text">
                        {getSelectedMicrophoneName()}
                      </span>
                      <DownOutlined className="dropdown-arrow" />
                    </div>
                  </Dropdown>
                </div>
                <div className="setting-item">
                  <button
                    className={`test-button ${isMicTesting ? 'testing' : ''}`}
                    onClick={testMicrophone}
                    disabled={!permissions.microphone}
                  >
                    {isMicTesting ? 'Stop Test' : 'Test Mic'}
                  </button>
                </div>
                <div className="setting-item">
                  <AudioLevelBlocks
                    level={testMicLevel}
                    isActive={isMicTesting}
                  />
                </div>
                <div className="setting-item">
                  <Slider
                    value={inputVolume}
                    onChange={setInputVolume}
                    style={{ width: '100%' }}
                    min={0}
                    max={100}
                    tooltip={{ formatter: (value) => `${value}%` }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Speaker Section */}
          <div className="settings-section">
            <div className="setting-row three-column speaker-section">
              <div className="setting-label-col">
                <span className="setting-label">Speaker</span>
              </div>
              <div className="setting-description-col">
                <div className="setting-item">
                  <span className="setting-sublabel">Choose Speaker</span>
                </div>
                <div className="setting-item">
                  <span className="setting-sublabel">Test Speaker</span>
                </div>
                <div className="setting-item">
                  <span className="setting-sublabel">Output Level:</span>
                </div>
                <div className="setting-item">
                  <span className="setting-sublabel">Output Volume:</span>
                </div>
              </div>
              <div className="setting-control-col">
                <div className="setting-item">
                  <Dropdown
                    menu={{ items: createSpeakerDropdownItems() }}
                    trigger={['click']}
                    placement="bottomLeft"
                    overlayClassName="device-settings-dropdown"
                    disabled={!permissions.microphone}
                  >
                    <div className="custom-device-select">
                      <span className="device-select-text">
                        {getSelectedSpeakerName()}
                      </span>
                      <DownOutlined className="dropdown-arrow" />
                    </div>
                  </Dropdown>
                </div>
                <div className="setting-item">
                  <button
                    className="test-button"
                    onClick={testSpeaker}
                    disabled={!permissions.microphone}
                  >
                    Test Speaker
                  </button>
                </div>
                <div className="setting-item">
                  <div className="level-indicator">
                    <Progress
                      percent={outputLevel}
                      showInfo={false}
                      strokeColor="#52c41a"
                      size="small"
                    />
                  </div>
                </div>
                <div className="setting-item">
                  <Slider
                    value={outputVolume}
                    onChange={setOutputVolume}
                    style={{ width: '100%' }}
                    min={0}
                    max={100}
                    tooltip={{ formatter: (value) => `${value}%` }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Audio Enhancement Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <NoiseCancellationIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Noise cancellation</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={noiseCancellation}
                  onChange={handleNoiseCancellation}
                />
                <span className="switch-status">
                  {noiseCancellation ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <EchoCancellationIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Echo cancellation</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={echoCancellation}
                  onChange={handleEchoCancellation}
                />
                <span className="switch-status">
                  {echoCancellation ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Auto-mute on joining meeting</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={autoMuteOnJoin}
                  onChange={setAutoMuteOnJoin}
                />
                <span className="switch-status">
                  {autoMuteOnJoin ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'video',
      label: (
        <div className="tab-label">
          <PiVideoCameraFill />
          <span>Video</span>
        </div>
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <div className="settings-header">
              <h3>Video Settings</h3>
              <p className="settings-description">Change your video settings here</p>
            </div>
          </div>

          {/* Camera Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <CameraIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Camera</span>
                  <span className="setting-sublabel">Choose Camera</span>
                </div>
              </div>
              <div className="setting-control">
                <Select
                  value={videoDeviceId}
                  onChange={handleVideoDeviceChange}
                  style={{ width: 200 }}
                  placeholder="Select camera"
                  disabled={!permissions.camera}
                >
                  {videoDevices.map(device => (
                    <Option key={device.deviceId} value={device.deviceId}>
                      {truncateDeviceName(device.label || 'Default Camera')}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>
          </div>

          {/* Video Enhancement Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <MirrorSelfIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Mirror video</span>
                  <span className="setting-sublabel">Flip your video horizontally to correct the orientation</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={isSelfVideoMirrored}
                  onChange={setIsSelfVideoMirrored}
                />
                <span className="switch-status">
                  {isSelfVideoMirrored ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Video brightness</span>
                  <span className="setting-sublabel">Adjust the brightness of your video to improve visibility</span>
                </div>
              </div>
              <div className="setting-control">
                <Slider
                  value={brightness}
                  onChange={onBrightnessChange}
                  style={{ width: 200 }}
                  min={50}
                  max={150}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <Modal
      open={open}
      onOk={handleClose}
      onCancel={handleClose}
      className="settings-prejoin-modal"
      footer={null}
      width="90%"
      style={{ maxWidth: '1000px' }}
      closeIcon={null}
      centered
      title={
        <div className="custom-modal-header">
          <span className="custom-modal-title">Settings</span>
          <div className="custom-close-button" onClick={handleClose}>
            <CloseOutlined />
          </div>
        </div>
      }
    >
      <Tabs
        defaultActiveKey="audio"
        items={tabItems}
        tabPosition="left"
        className="settings-tabs"
      />
    </Modal>
  );
}