@import "../../styles/variables";

.settings-prejoin-modal {
  // Ensure perfect centering
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    height: 70vh;


    .ant-modal-header {
        padding: 15px 30px 15px 30px;
      border-bottom: 1px solid #D7D7D7;
      margin-bottom: 0;
      flex-shrink: 0;

      .ant-modal-title {
        margin: 0;
        padding: 0;
        line-height: 1;
        width: 100%;
        height: 100%;
      }

      .custom-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;

        .custom-modal-title {
          font-size: 18px;
          font-weight: 500;
          color: #636363;
          flex: 1;
        }

        .custom-close-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
          color: #666;
          font-size: 16px;

          &:hover {
            background-color: #f5f5f5;
            color: #333;
          }
        }
      }
    }

    // Hide default close button
    .ant-modal-close {
      display: none;
    }

    .ant-modal-body {
      padding: 0;
      height: calc(100% - 60px);
      overflow: hidden;

      .settings-tabs {
        height: 100%;
        display: flex;
        flex-direction: row;

        .ant-tabs-nav {
          width: 240px;
          margin: 0;
          flex-shrink: 0;
          background-color: #f7f7f7;
          padding: 15px;


          .ant-tabs-nav-list {
            width: 100%;


            .ant-tabs-tab {
              padding: 10px;
              margin: 0;
              border-radius: 0;
              width: 100%;
              border-radius: 5px;

              &.ant-tabs-tab-active {
                background-color: #e4e9f5;
                border-color: transparent;
              }

              .tab-label {
                font-family: $font;
                font-weight: 500;
                font-size: 16px;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                width: 100%;

                svg {
                  width: 22px;
                  height: 22px;
                  flex-shrink: 0;
                }

                span {
                  white-space: nowrap;
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: 2px solid #eff1f4;
          height: 100%;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              padding: 0;
              overflow-y: auto;
              scrollbar-width: thin;
              scrollbar-color: transparent transparent;
              transition: scrollbar-color 0.3s ease;

              // Webkit browsers (Chrome, Safari, Edge)
              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: transparent;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: transparent;
                border-radius: 3px;
                transition: background 0.3s ease;
              }

              // Show scrollbar on hover
              &:hover {
                scrollbar-color: #888 #f1f1f1;

                &::-webkit-scrollbar-track {
                  background: #f1f1f1;
                }

                &::-webkit-scrollbar-thumb {
                  background: #888;
                }
              }

              // Show scrollbar during scroll
              &.scrolling {
                scrollbar-color: #888 #f1f1f1;

                &::-webkit-scrollbar-track {
                  background: #f1f1f1;
                }

                &::-webkit-scrollbar-thumb {
                  background: #888;
                }
              }
            }
          }
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }
  }
}

.settings-content {
  padding: 0 1rem 0 1rem;
  height: auto;
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;

  // Webkit browsers (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
    transition: background 0.3s ease;
  }

  // Show scrollbar on hover
  &:hover {
    scrollbar-color: #888 #f1f1f1;

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }
  }

  // Show scrollbar during scroll
  &.scrolling {
    scrollbar-color: #888 #f1f1f1;

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }
  }
}

.settings-section {
  margin-bottom: 2rem;

  .settings-header {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 0px 15px 0px;

    h3 {
      font-size: 20px;
      font-family: Inter;
      font-weight: 600;
      color: #3B60E4;
      margin: 0;
      line-height: 1;
    }

    .settings-description {
      color: #666;
      font-size: 12px;
      margin: 0;
      line-height: 1;
      padding-bottom: 2.3px;
    }
  }

  h3 {
    font-size: 22px;
    font-family: $font;
    font-weight: 600;
    color: #000;
    margin-bottom: 0.5rem;
  }

  .settings-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 1.5rem;
  }
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  // Three column layout for microphone and speaker device selection
  &.three-column {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    gap: 1rem;
    align-items: flex-start;

    .setting-label-col {
      display: flex;
      align-items: flex-start;
      padding-top: 0.5rem;

      .setting-label {
        font-size: 16px;
        font-weight: 500;
        color: #000;
        font-family: $font;
      }
    }

    .setting-description-col {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .setting-item {
        display: flex;
        align-items: center;
        height: 32px;
        justify-content: flex-start;

        .setting-sublabel {
          font-size: 14px;
          color: #666;
          font-family: $font;
        }
      }
    }

    .setting-control-col {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .setting-item {
        display: flex;
        align-items: center;
        height: 32px;

        .ant-select {
          width: 100%;
          height: 32px;

          .ant-select-selector {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            height: 32px !important;

            &:hover {
              border-color: #3B60E4;
            }
          }

          &.ant-select-focused .ant-select-selector {
            border-color: #3B60E4;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .custom-device-select {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 12px;
          border: 1px solid #a8a8a8;
          border-radius: 3px;
          background-color: #fff;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #3B60E4;
          }

          .device-select-text {
            font-size: 13px;
            color: #555454;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
          }

          .dropdown-arrow {
            color: #555454;
            font-size: 12px;
            margin-left: 8px;
            flex-shrink: 0;
          }

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .test-button {
          background-color: #f0f0f0;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          padding: 6px 12px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover:not(:disabled) {
            background-color: #e6f7ff;
            border-color: #40a9ff;
            color: #1890ff;
          }

          &.testing {
            background-color: #277bf7;
            border-color: #277bf7;
            color: white;

            &:hover {
              background-color: #1e6bd6;
              border-color: #1e6bd6;
              color: white;
            }
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .level-indicator {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;

          .ant-progress {
            width: 100%;

            .ant-progress-bg {
              transition: width 0.1s ease;
            }
          }
        }

        .audio-level-blocks {
          display: flex;
          align-items: center;
          gap: 2px;
          height: 32px;
          width: 100%;

          .audio-block {
            flex: 1;
            height: 12px;
            background-color: #d9d9d9;
            border-radius: 2px;
            transition: all 0.15s ease;

            &.active {
              background-color: #277bf7;
              box-shadow: 0 0 6px rgba(39, 123, 247, 0.4);
              transform: scaleY(1.2);
            }
          }
        }

        .ant-slider {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;
          margin: 0;

          .ant-slider-rail {
            background-color: #f0f0f0;
          }

          .ant-slider-track {
            background-color: #277bf7;
          }

          .ant-slider-handle {
            border-color: #277bf7;

            &:hover {
              border-color: #1e6bd6;
            }

            &:focus {
              border-color: #1e6bd6;
              box-shadow: 0 0 0 5px rgba(39, 123, 247, 0.12);
            }
          }
        }
      }
    }
  }
}

// Custom dropdown styles matching MicDeviceDropdown
.device-settings-dropdown {
  .ant-dropdown-menu {
    background: #FFFFFF;
    border-radius: 8px !important;
    min-width: 250px;
    max-width: 90vw;
    padding: 4px;
    border: 1px solid #E3E3E3;
    margin-top: 8px;

    .ant-dropdown-menu-item {
      padding: 0;
      margin: 2px 0;
      width: 100%;

      &:hover {
        background: #F5F5F5;
      }
    }
  }
}

.device-dropdown-item {
  display: flex;
  align-items: center;
  padding: 6px;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
  height: 100%;

  &.selected {
    background-color: #E3E3E3;
    padding: 6px;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .device-name {
    font-size: 13px;
    color: #555454;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;

  .setting-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(90deg, rgba(59, 96, 228, 1) 0%, rgba(86, 123, 255, 1) 100%);
    border-radius: 50%;
    padding: 4px;
    flex-shrink: 0;
  }

  .setting-details {
    display: flex;
    flex-direction: column;

    .setting-label {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      font-family: $font;
    }

    .setting-sublabel {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }
  }

  .setting-label {
    font-size: 16px;
    font-weight: 500;
    color: #000;
    font-family: $font;
  }
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .ant-select {
    .ant-select-selector {
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      &:hover {
        border-color: #3B60E4;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #3B60E4;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-slider {
    .ant-slider-rail {
      background-color: #f0f0f0;
    }

    .ant-slider-track {
      background-color: #277bf7;
    }

    .ant-slider-handle {
      border-color: #277bf7;

      &:hover {
        border-color: #1e6bd6;
      }

      &:focus {
        border-color: #1e6bd6;
        box-shadow: 0 0 0 5px rgba(39, 123, 247, 0.12);
      }
    }
  }

  .ant-switch-checked {
    background-color: #277bf7 !important;
  }

  .switch-status {
    font-size: 12px;
    color: #666;
    margin-left: 0.5rem;
  }

  .test-button {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background-color: #e6f7ff;
      border-color: #40a9ff;
      color: #1890ff;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .level-indicator {
    width: 200px;

    .ant-progress {
      .ant-progress-bg {
        transition: width 0.1s ease;
      }
    }
  }

  .brightness-value {
    font-family: $font;
    font-weight: 500;
    color: #666;
    min-width: 40px;
    text-align: right;
    margin-left: 0.5rem;
  }
}

// Responsive design
@media screen and (max-width: 1200px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 70vw !important;
    }
  }
}

@media screen and (max-width: 1000px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 80vw !important;
    }
  }
}

@media screen and (max-width: 800px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 90vw !important;
      height: 70vh;
    }

    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 220px;
        }
      }
    }
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    &.three-column {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .setting-label-col {
        width: 100%;
        padding-top: 0;

        .setting-label {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 1rem;
        }
      }

      .setting-description-col,
      .setting-control-col {
        width: 100%;
      }

      .setting-description-col {
        .setting-item {
          margin-bottom: 0.5rem;

          .setting-sublabel {
            font-size: 16px;
            font-weight: 500;
          }
        }
      }

      .setting-control-col {
        .setting-item {
          margin-bottom: 1rem;
        }
      }
    }
  }

  .setting-control {
    width: 100%;
    justify-content: flex-end;
  }
}

@media screen and (max-width: 600px) {
  .settings-prejoin-modal {
    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 200px;

          .ant-tabs-tab {
            padding: 10px;

            .tab-label {
              gap: 0.5rem;

              svg {
                width: 18px;
                height: 18px;
              }

              span {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}