import {
  createLocalAudioTrack,
  createLocalVideoTrack,
  facingModeFromLocalTrack,
  Track,
  VideoPresets,
  Mutex,
} from "livekit-client";
import { BackgroundBlur, VirtualBackground } from "@livekit/track-processors";
import * as React from "react";

import {
  UserOutlined,
  AudioOutlined,
  AudioMutedOutlined,
  SettingOutlined,
  CaretDownFilled,
} from "@ant-design/icons";

import {
  // MediaDeviceMenu,
  // TrackToggle,
  useMediaDevices,
  usePersistentUserChoices,
} from "@livekit/components-react";
import { generateAvatar } from "../utils/helper";
import "../styles/Prejoin.scss";
import "../styles/audioVideoPrejoin.scss";
import "../styles/videoHeightControl.scss";
// import AudioVideoSettingsModal from "../components/AudioVideoSettingsModal/AudioVideoSettingsModal";
// import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
// import AudioSettings from "../components/AudioVideoSettingsModal/AudioSettings";
// import VideoSettings from "../components/AudioVideoSettingsModal/VideoSettings";
import { ReactComponent as VirtualBackgroundIcon } from "./icons/Virtualbackground.svg";
import { ReactComponent as WarningIcon } from "./icons/waringIcon.svg";
import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
import SettingsPrejoin from "../components/AudioVideoSettingsModal/SettingsPrejoin";
import PermissionUi, {
  isMobileBrowser,
  getBrowserType,
  isSafari,
  requiresUserGesture,
  getUserMediaWithBrowserStrategy,
  checkPermissionsWithBrowserStrategy
} from '../components/permissionUi/permissionUi';
import { ReactComponent as VideoOff } from "./icons/VideoOff.svg";
import { ReactComponent as VideoOn } from "./icons/VideoOn.svg";
import MicDeviceDropdown from "../components/AudioVideoSettingsModal/MicDeviceDropdown";
import CameraDeviceDropdown from "../components/AudioVideoSettingsModal/CameraDeviceDropdown";
/**
 * @public
 */
// Separate hook for audio track management
function useAudioTrack(audioEnabled, audioDeviceId, onError, setToastMessage, setToastStatus, setShowToast) {
  const [audioTrack, setAudioTrack] = React.useState(null);
  const audioTrackRef = React.useRef(null);
  const prevAudioDeviceId = React.useRef(null);
  const prevAudioEnabled = React.useRef(null);
  const trackLock = React.useMemo(() => new Mutex(), []);

  React.useEffect(() => {
    let needsCleanup = false;

    trackLock.lock().then(async (unlock) => {
      try {
        const audioDeviceChanged = prevAudioDeviceId.current !== audioDeviceId;
        const audioEnabledChanged = prevAudioEnabled.current !== audioEnabled;
        const isInitialMount = prevAudioEnabled.current === null;



        // Handle initial mount or actual changes
        if (isInitialMount || audioEnabledChanged || audioDeviceChanged) {
          if (audioEnabled) {
            if (!audioTrackRef.current || audioDeviceChanged) {
              // Create new audio track only if none exists or device changed
              if (audioTrackRef.current) {
                audioTrackRef.current.stop();
              }
              console.log('🎵 Creating audio track with deviceId:', audioDeviceId);
              audioTrackRef.current = await createLocalAudioTrack({ deviceId: audioDeviceId });
              console.log('🎵 Audio track created:', audioTrackRef.current);
              if (!needsCleanup) {
                setAudioTrack(audioTrackRef.current);
                console.log('🎵 Audio track set in state');
              }
            } else if (audioEnabledChanged && audioTrackRef.current && audioTrackRef.current.isMuted) {
              // Use existing track and unmute when enabled
              await audioTrackRef.current.unmute();
            }
          } else if (!isInitialMount && audioTrackRef.current && !audioTrackRef.current.isMuted) {
            // Mute instead of stopping when disabled (but not on initial mount)
            await audioTrackRef.current.mute();
          }
        }

        prevAudioDeviceId.current = audioDeviceId;
        prevAudioEnabled.current = audioEnabled;

      } catch (e) {
        if (onError && e instanceof Error) {
          onError(e);
        } else {
          console.error("Error managing audio track:", e);
          setToastMessage("Permissions denied for accessing audio devices. Please check your browser settings.");
          setToastStatus("error");
          setShowToast(true);
        }
      } finally {
        unlock();
      }
    });

    return () => {
      needsCleanup = true;
    };
  }, [audioEnabled, audioDeviceId, onError, trackLock]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (audioTrackRef.current) {
        audioTrackRef.current.stop();
        audioTrackRef.current = null;
      }
    };
  }, []);

  return audioTrack;
}

// Separate hook for video track management
function useVideoTrack(videoEnabled, videoDeviceId, onError, setToastMessage, setToastStatus, setShowToast) {
  const [videoTrack, setVideoTrack] = React.useState(null);
  const videoTrackRef = React.useRef(null);
  const prevVideoDeviceId = React.useRef(null);
  const prevVideoEnabled = React.useRef(null);
  const trackLock = React.useMemo(() => new Mutex(), []);

  React.useEffect(() => {
    let needsCleanup = false;

    trackLock.lock().then(async (unlock) => {
      try {
        const videoDeviceChanged = prevVideoDeviceId.current !== videoDeviceId;
        const videoEnabledChanged = prevVideoEnabled.current !== videoEnabled;

        // Only act when there's an actual change
        if (videoEnabledChanged || videoDeviceChanged) {
          if (videoEnabled) {
            if (!videoTrackRef.current || videoDeviceChanged) {
              // Create new video track only if none exists or device changed
              if (videoTrackRef.current) {
                videoTrackRef.current.stop();
              }
              videoTrackRef.current = await createLocalVideoTrack({
                deviceId: videoDeviceId,
                resolution: VideoPresets.h720.resolution,
              });
              if (!needsCleanup) {
                setVideoTrack(videoTrackRef.current);
              }
            } else if (videoTrackRef.current && videoTrackRef.current.isMuted) {
              // Use existing track and unmute when enabled
              await videoTrackRef.current.unmute();
            }
          } else if (videoTrackRef.current && !videoTrackRef.current.isMuted) {
            // Mute instead of stopping when disabled
            await videoTrackRef.current.mute();
          }
        }

        prevVideoDeviceId.current = videoDeviceId;
        prevVideoEnabled.current = videoEnabled;

      } catch (e) {
        if (onError && e instanceof Error) {
          onError(e);
        } else {
          console.error("Error managing video track:", e);
          setToastMessage("Permissions denied for accessing video devices. Please check your browser settings.");
          setToastStatus("error");
          setShowToast(true);
        }
      } finally {
        unlock();
      }
    });

    return () => {
      needsCleanup = true;
    };
  }, [videoEnabled, videoDeviceId, onError, trackLock]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (videoTrackRef.current) {
        videoTrackRef.current.stop();
        videoTrackRef.current = null;
      }
    };
  }, []);

  return videoTrack;
}

export function usePreviewTracks(options, onError, setToastMessage, setToastStatus, setShowToast) {
  const audioEnabled = !!options.audio;
  const videoEnabled = !!options.video;

  // Preserve device IDs even when disabled to prevent unnecessary track recreation
  const audioDeviceIdRef = React.useRef(null);
  const videoDeviceIdRef = React.useRef(null);

  // Update device IDs only when they're actually provided
  if (options.audio?.deviceId) {
    audioDeviceIdRef.current = options.audio.deviceId;
  }
  if (options.video?.deviceId) {
    videoDeviceIdRef.current = options.video.deviceId;
  }

  const audioTrack = useAudioTrack(audioEnabled, audioDeviceIdRef.current, onError, setToastMessage, setToastStatus, setShowToast);
  const videoTrack = useVideoTrack(videoEnabled, videoDeviceIdRef.current, onError, setToastMessage, setToastStatus, setShowToast);

  // Return both individual tracks and combined tracks array for backward compatibility
  return {
    tracks: [audioTrack, videoTrack].filter(Boolean),
    audioTrack,
    videoTrack
  };
}

/** @public */
export function usePreviewDevice(enabled, deviceId, deviceKind) {
  const [deviceError, setDeviceError] = React.useState(null);
  const [isCreatingTrack, setIsCreatingTrack] = React.useState(false);

  const devices = useMediaDevices({ kind: deviceKind });
  const [selectedDevice, setSelectedDevice] = React.useState(undefined);

  const [localTrack, setLocalTrack] = React.useState();
  const [localDeviceId, setLocalDeviceId] = React.useState(deviceId);

  React.useEffect(() => {
    setLocalDeviceId(deviceId);
  }, [deviceId]);

  const prevDeviceId = React.useRef(localDeviceId); // Moved prevDeviceId declaration here

  const createTrack = async (id, kind) => {
    try {
      const track =
        kind === "videoinput"
          ? await createLocalVideoTrack({
              deviceId: id,
              resolution: VideoPresets.h720.resolution,
            })
          : await createLocalAudioTrack({ deviceId: id });

      const newDeviceId = await track.getDeviceId();
      if (newDeviceId && id !== newDeviceId) {
        prevDeviceId.current = newDeviceId;
        setLocalDeviceId(newDeviceId);
      }
      setLocalTrack(track);
    } catch (e) {
      if (e instanceof Error) {
        setDeviceError(e);
      }
    }
  };

  const switchDevice = async (track, id) => {
    await track.setDeviceId(id);
    prevDeviceId.current = id;
  };

  React.useEffect(() => {
    if (enabled && !localTrack && !deviceError && !isCreatingTrack) {
      setIsCreatingTrack(true);
      createTrack(localDeviceId, deviceKind).finally(() => {
        setIsCreatingTrack(false);
      });
    }
  }, [
    enabled,
    localTrack,
    deviceError,
    isCreatingTrack,
    localDeviceId,
    deviceKind,
  ]);

  React.useEffect(() => {
    if (!localTrack) {
      return;
    }
    if (!enabled) {
      localTrack.mute().then(() => console.log(localTrack.mediaStreamTrack));
    } else if (
      selectedDevice?.deviceId &&
      prevDeviceId.current !== selectedDevice?.deviceId
    ) {
      switchDevice(localTrack, selectedDevice.deviceId);
    } else {
      localTrack.unmute();
    }
  }, [localTrack, selectedDevice, enabled, deviceKind]);

  React.useEffect(() => {
    return () => {
      if (localTrack) {
        localTrack.stop();
        localTrack.mute();
      }
    };
  }, [localTrack]);

  React.useEffect(() => {
    setSelectedDevice(devices?.find((dev) => dev.deviceId === localDeviceId));
  }, [localDeviceId, devices]);

  return {
    selectedDevice,
    localTrack,
    deviceError,
  };
}

export function PreJoinAudioVideo({
  defaults = {},
  onValidate,
  onError,
  username,
  setIsValid,
  setUserChoices,
  persistUserChoices = true,
  setToastMessage,
  setToastStatus,
  setShowToast,
  setToastNotification,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setDeviceIdAudio,
  brightness = 100,
  onBrightnessChange,
  currentEffect,
  setCurrentEffect,
  ...htmlProps
}) {
  const [isVisualEffectsModalOpen, setIsVisualEffectsModalOpen] = React.useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = React.useState(false);
  const [showPermissionModal, setShowPermissionModal] = React.useState(false);

  // Commented out for future use
  // const [noiseCancellation, setNoiseCancellation] = React.useState(
  //   room?.options?.audioCaptureDefaults?.noiseSuppression
  // );
  // const [echoCancellation, setEchoCancellation] = React.useState(
  //   room?.options?.audioCaptureDefaults?.echoCancellation
  // );

  // Add state for individual permissions - default to false to show warning borders initially
  const [permissions, setPermissions] = React.useState({
    camera: false,
    microphone: false
  });

  // Add state for permission type
  const [permissionType, setPermissionType] = React.useState('both');

  const partialDefaults = {
    ...(defaults.audioDeviceId !== undefined && {
      audioDeviceId: defaults.audioDeviceId,
    }),
    ...(defaults.videoDeviceId !== undefined && {
      videoDeviceId: defaults.videoDeviceId,
    }),
    audioEnabled: false,
    videoEnabled: false,
    ...(defaults.username !== undefined && { username: defaults.username }),
  };

  const {
    userChoices: initialUserChoices,
    saveAudioInputDeviceId,
    saveAudioInputEnabled,
    saveVideoInputDeviceId,
    saveVideoInputEnabled,
    saveUsername,
  } = usePersistentUserChoices({
    defaults: partialDefaults,
    preventSave: !persistUserChoices,
    preventLoad: !persistUserChoices,
  });

  const [audioEnabled, setAudioEnabled] = React.useState(false);
  const [videoEnabled, setVideoEnabled] = React.useState(false);
  const [audioDeviceId, setAudioDeviceId] = React.useState(
    initialUserChoices.audioDeviceId
  );
  const [videoDeviceId, setVideoDeviceId] = React.useState(
    initialUserChoices.videoDeviceId
  );
  // const [speakerDeviceId, setSpeakerDeviceId] = React.useState("");
  const [avatarName, setAvatarName] = React.useState("");

  React.useEffect(() => {
    saveAudioInputEnabled(audioEnabled);
  }, [audioEnabled, saveAudioInputEnabled]);
  React.useEffect(() => {
    saveVideoInputEnabled(videoEnabled);
  }, [videoEnabled, saveVideoInputEnabled]);
  React.useEffect(() => {
    saveAudioInputDeviceId(audioDeviceId);
  }, [audioDeviceId, saveAudioInputDeviceId]);
  React.useEffect(() => {
    saveVideoInputDeviceId(videoDeviceId);
  }, [videoDeviceId, saveVideoInputDeviceId]);
  React.useEffect(() => {
    saveUsername(username);
  }, [username, saveUsername]);

  React.useEffect(() => {
    if (username) {
      setAvatarName(generateAvatar(username));
    } else {
      setAvatarName(null);
    }
  }, [username]);



  const { tracks, videoTrack } = usePreviewTracks(
    {
      audio: audioEnabled
        ? { deviceId: initialUserChoices.audioDeviceId }
        : false,
      video: videoEnabled
        ? { deviceId: initialUserChoices.videoDeviceId }
        : false,
    },
    onError,
    setToastMessage,
    setToastStatus,
    setShowToast
  );

  const videoEl = React.useRef(null);

  const trackFacingMode = React.useMemo(() => {
    if (videoTrack) {
      const { facingMode } = facingModeFromLocalTrack(videoTrack);
      return facingMode;
    } else {
      return "undefined";
    }
  }, [videoTrack]);

  // const audioTrack = React.useMemo(
  //   () => tracks?.filter((track) => track.kind === Track.Kind.Audio)[0],
  //   [tracks]
  // );

  React.useEffect(() => {
    if (videoEl.current && videoTrack && videoEnabled) {
      videoTrack.unmute();
      videoTrack.attach(videoEl.current);
    }

    return () => {
      if (videoTrack) {
        videoTrack.detach();
      }
    };
  }, [videoTrack, videoEnabled]);

  // Apply brightness effect to PreJoin video element
  React.useEffect(() => {
    if (videoEl.current) {
      videoEl.current.style.filter = `brightness(${brightness}%)`;
    }
  }, [brightness]);

  // Effect to reapply virtual background when video track changes
  React.useEffect(() => {
    const reapplyEffect = async () => {
      if (videoTrack && currentEffect && videoEnabled) {
        try {
          // Small delay to ensure track is fully initialized
          await new Promise((resolve) => {
            setTimeout(() => resolve(), 100);
          });

          // Ensure video track is enabled and unmuted
          if (!videoTrack.isEnabled) {
            await videoTrack.enable();
          }
          if (videoTrack.isMuted) {
            await videoTrack.unmute();
          }

          // Apply the current effect
          if (currentEffect.type === 'blur') {
            await videoTrack.setProcessor(BackgroundBlur(currentEffect.value, { delegate: "GPU" }));
          } else if (currentEffect.type === 'background') {
            await videoTrack.setProcessor(VirtualBackground(currentEffect.value));
          }


        } catch (error) {
          console.error('Error reapplying virtual background effect:', error);
        }
      }
    };

    reapplyEffect();
  }, [videoTrack, videoEnabled]); // Reapply when videoTrack or videoEnabled changes

  const handleValidation = React.useCallback(
    (values) => {
      if (typeof onValidate === "function") {
        return onValidate(values);
      } else {
        return values.username !== "";
      }
    },
    [onValidate]
  );

  React.useEffect(() => {
    const newUserChoices = {
      username,
      videoEnabled,
      videoDeviceId,
      audioEnabled,
      audioDeviceId,
    };
    setUserChoices(newUserChoices);
    setIsValid(handleValidation(newUserChoices));
  }, [
    username,
    videoEnabled,
    videoDeviceId,
    audioEnabled,
    audioDeviceId,
    handleValidation,
  ]);

  React.useEffect(() => {
    setAudioDeviceId(audioDeviceId);
    setDeviceIdAudio(audioDeviceId);
    setVideoDeviceId(videoDeviceId);
  }, [
    audioDeviceId,
    videoTrack,
    videoDeviceId,
    isSettingsModalOpen
  ]);

  // Browser-specific permission checking with real-time updates
  const checkPermissionsRealTime = React.useCallback(async () => {
    try {
      const result = await checkPermissionsWithBrowserStrategy();

      // Log permission states for debugging
      console.log('Permission states:', {
        browser: getBrowserType(),
        camera: result.cameraState,
        microphone: result.microphoneState,
        requiresUserGesture: result.requiresUserGesture
      });

      // Update permissions state
      setPermissions({
        camera: result.camera,
        microphone: result.microphone
      });

      // Handle permission modal display logic
      const hasPermissions = result.camera && result.microphone;
      const shouldShowModal = !hasPermissions && !isMobileBrowser() && !result.requiresUserGesture;

      if (shouldShowModal) {
        if (!result.camera && !result.microphone) {
          setPermissionType('both');
        } else if (!result.camera) {
          setPermissionType('camera');
        } else if (!result.microphone) {
          setPermissionType('mic');
        }
        setShowPermissionModal(true);
      } else {
        setShowPermissionModal(false);
      }

      // Set up permission change listeners for browsers that support it
      if (result.cameraPermission && result.micPermission) {
        const handleCameraChange = () => {
          const newCameraState = result.cameraPermission.state === 'granted';
          const newMicState = result.micPermission.state === 'granted';

          setPermissions(prev => ({
            ...prev,
            camera: newCameraState
          }));

          const newHasPermissions = newCameraState && newMicState;
          const newShouldShowModal = !newHasPermissions && !isMobileBrowser();

          if (newShouldShowModal) {
            if (!newCameraState && !newMicState) {
              setPermissionType('both');
            } else if (!newCameraState) {
              setPermissionType('camera');
            } else if (!newMicState) {
              setPermissionType('mic');
            }
            setShowPermissionModal(true);
          } else {
            setShowPermissionModal(false);
          }
        };

        const handleMicChange = () => {
          const newCameraState = result.cameraPermission.state === 'granted';
          const newMicState = result.micPermission.state === 'granted';

          setPermissions(prev => ({
            ...prev,
            microphone: newMicState
          }));

          const newHasPermissions = newCameraState && newMicState;
          const newShouldShowModal = !newHasPermissions && !isMobileBrowser();

          if (newShouldShowModal) {
            if (!newCameraState && !newMicState) {
              setPermissionType('both');
            } else if (!newCameraState) {
              setPermissionType('camera');
            } else if (!newMicState) {
              setPermissionType('mic');
            }
            setShowPermissionModal(true);
          } else {
            setShowPermissionModal(false);
          }
        };

        // Replace the existing event listeners
        result.cameraPermission.removeEventListener('change', () => {});
        result.micPermission.removeEventListener('change', () => {});
        result.cameraPermission.addEventListener('change', handleCameraChange);
        result.micPermission.addEventListener('change', handleMicChange);

        // Update cleanup function
        const originalCleanup = result.cleanup;
        result.cleanup = () => {
          result.cameraPermission.removeEventListener('change', handleCameraChange);
          result.micPermission.removeEventListener('change', handleMicChange);
          originalCleanup();
        };
      }

      return result;
    } catch (error) {
      console.error('Permission check error:', error);
      setPermissions({
        camera: false,
        microphone: false
      });
      if (!isMobileBrowser()) {
        setShowPermissionModal(true);
      }
      return {
        cleanup: () => {},
        camera: false,
        microphone: false,
        cameraState: 'denied',
        microphoneState: 'denied',
        requiresUserGesture: requiresUserGesture()
      };
    }
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  // Update permissions state when checking
  React.useEffect(() => {
    let cleanup = () => {};

    const setupPermissions = async () => {
      try {
        console.log('Setting up permissions - initializing to false');
        // Initialize permissions to false to ensure warning borders show immediately
        setPermissions({
          camera: false,
          microphone: false
        });

        const result = await checkPermissionsRealTime();
        cleanup = result.cleanup;

        console.log('Permission check result:', {
          camera: result.camera,
          microphone: result.microphone,
          cameraState: result.cameraState,
          microphoneState: result.microphoneState
        });

        // Ensure we update the permissions state with the returned values
        setPermissions({
          camera: result.camera,
          microphone: result.microphone
        });
      } catch (error) {
        console.error('Error setting up permissions:', error);
      }
    };

    setupPermissions();

    return () => {
      cleanup();
    };
  }, []); // Only run on mount

  // Browser-specific permission request with fallback strategies
  const requestPermissionsWithBrowserStrategy = async (type) => {
    const browserType = getBrowserType();

    try {
      let constraints = {};

      // Set up constraints based on request type
      switch (type) {
        case 'camera':
          constraints = { video: true };
          break;
        case 'mic':
          constraints = { audio: true };
          break;
        case 'mic_camera':
          constraints = { video: true, audio: true };
          break;
        default:
          throw new Error('Invalid permission type');
      }

      // Use browser-specific getUserMedia strategy
      const stream = await getUserMediaWithBrowserStrategy(constraints);

      // Clean up the stream immediately
      stream.getTracks().forEach(track => track.stop());

      // Note: With persistent tracks using mute/unmute, we don't need to
      // refresh tracks when permissions are granted. The tracks will be
      // automatically unmuted when the user enables them.

      // Update permissions state
      setPermissions(prev => ({
        ...prev,
        camera: type === 'camera' || type === 'mic_camera' ? true : prev.camera,
        microphone: type === 'mic' || type === 'mic_camera' ? true : prev.microphone
      }));

      return true;
    } catch (error) {
      console.error(`Permission request error for ${type} in ${browserType}:`, error);

      // Handle specific error types
      let errorMessage = '';
      switch (error.name) {
        case 'NotAllowedError':
          errorMessage = `${type === 'camera' ? 'Camera' : type === 'mic' ? 'Microphone' : 'Camera and microphone'} access was denied. Please allow access to continue.`;
          break;
        case 'NotFoundError':
          errorMessage = `No ${type === 'camera' ? 'camera' : type === 'mic' ? 'microphone' : 'camera or microphone'} devices found.`;
          break;
        case 'OverconstrainedError':
          errorMessage = `${type === 'camera' ? 'Camera' : type === 'mic' ? 'Microphone' : 'Media'} constraints could not be satisfied.`;
          break;
        default:
          errorMessage = `Failed to access ${type === 'camera' ? 'camera' : type === 'mic' ? 'microphone' : 'camera and microphone'}. Please try again.`;
      }

      setPermissions(prev => ({
        ...prev,
        camera: type === 'camera' || type === 'mic_camera' ? false : prev.camera,
        microphone: type === 'mic' || type === 'mic_camera' ? false : prev.microphone
      }));

      setToastMessage(errorMessage);
      setToastStatus("error");
      setShowToast(true);

      return false;
    }
  };

  // Browser-aware click handlers
  const handleAudioClick = async () => {
    // For Safari/iOS or mobile browsers, request permission when user tries to enable
    if (!permissions.microphone && (isSafari() || isMobileBrowser())) {
      await requestPermissionsWithBrowserStrategy('mic');
    }
    setAudioEnabled(!audioEnabled);
  };

  const handleVideoClick = async () => {
    // For Safari/iOS or mobile browsers, request permission when user tries to enable
    if (!permissions.camera && (isSafari() || isMobileBrowser())) {
      await requestPermissionsWithBrowserStrategy('camera');
    }
    setVideoEnabled(!videoEnabled);
  };

  // Update checkPermissions to use the real-time check
  const checkPermissions = async () => {
    try {
      const result = await checkPermissionsRealTime();

      // Update permissions state with the returned values
      setPermissions({
        camera: result.camera,
        microphone: result.microphone
      });

      return result.camera && result.microphone;
    } catch (error) {
      console.error('Permission check error:', error);
      setPermissions({
        camera: false,
        microphone: false
      });
      setShowPermissionModal(true);
      return false;
    }
  };

  // Check permissions when component mounts
  React.useEffect(() => {
    checkPermissions();
  }, []);

  const handleBrightnessChange = (value) => {
    if (onBrightnessChange) {
      onBrightnessChange(value);
    }
  };

  const handleEffectSelected = async (effectType, value) => {
    // Store the effect regardless of whether camera is currently enabled
    const newEffect = effectType === 'none' ? null : { type: effectType, value };
    setCurrentEffect(newEffect);

    // If camera is not enabled, just store the effect for later application
    if (!videoTrack || !videoEnabled) {
      if (!videoEnabled) {
        setToastMessage('Virtual background effect saved. It will be applied when you enable your camera.');
        setToastStatus('success');
        setShowToast(true);
      }
      return;
    }

    try {
      // Ensure video track is enabled and unmuted
      if (!videoTrack.isEnabled) {
        await videoTrack.enable();
      }
      if (videoTrack.isMuted) {
        await videoTrack.unmute();
      }

      // Remove any existing processor
      if (videoTrack.getProcessor()) {
        await videoTrack.stopProcessor();
      }

      // Apply new effect
      if (effectType === 'none') {
        // No effect needed, processor already stopped
      } else if (effectType === 'blur') {
        await videoTrack.setProcessor(BackgroundBlur(value, { delegate: "GPU" }));
      } else if (effectType === 'background') {
        await videoTrack.setProcessor(VirtualBackground(value));
      }

      console.log('Virtual background effect applied:', newEffect);
    } catch (error) {
      console.error('Error applying effect:', error);
      setToastMessage('Failed to apply virtual background effect. Please ensure your camera is working properly.');
      setToastStatus('error');
      setShowToast(true);
    }
  };

  return (
    <div className="video-prejoin-container lk-prejoin" {...htmlProps}>
      <div
        className="mute-button"
        onClick={() => setAudioEnabled(!audioEnabled)}
      >
        {audioEnabled ? (
          <AudioOutlined className="control-icon" />
        ) : (
          <AudioMutedOutlined className="control-icon" />
        )}
      </div>
      <div className="lk-video-container">
        {videoTrack && videoEnabled ? (
          <video
            ref={videoEl}
            width="100%"
            height="100%"
            data-lk-facing-mode={trackFacingMode}
            className={isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}
            style={{
              objectFit: "cover",
              borderRadius: "15px",
              display: "block"
            }}
          >
            <track kind="captions" />
          </video>
        ) : (
          <div className="lk-camera-off-note">
            <div className="avatar-container">
              {avatarName || <UserOutlined className="avatar-icon" />}
            </div>
          </div>
        )}
      </div>
      <div
        className="lk-button-group-container control-bar-container"
      >
        {/* Audio Button with Dropdown */}
        <div className="lk-button-group audio" style={{ position: "relative" }}>
          <div
            className={`control-button ${!permissions.microphone ? 'permission-warning' : ''}`}
            onClick={handleAudioClick}
          >
            {audioEnabled ? (
              <AudioOutlined className="control-icon" />
            ) : (
              <AudioMutedOutlined className="control-icon" />
            )}
            {!permissions.microphone && (
              <WarningIcon className="warning-icon" />
            )}
          </div>

          {/* Mic Device Dropdown */}
          <MicDeviceDropdown
            deviceId={audioDeviceId}
            setDeviceId={setAudioDeviceId}
            track={tracks?.filter((track) => track.kind === Track.Kind.Audio)[0]}
            permissionsGranted={permissions.microphone}
          >
            <div className="dropdown-button">
              <CaretDownFilled className="dropdown-icon" />
            </div>
          </MicDeviceDropdown>
        </div>

        {/* Video Button with Dropdown */}
        <div className="lk-button-group video">
          <div
            className={`control-button ${!permissions.camera ? 'permission-warning' : ''}`}
            onClick={handleVideoClick}
          >
            {videoEnabled ? (
              <VideoOn className="control-icon" />
            ) : (
              <VideoOff className="control-icon" />
            )}
            {!permissions.camera && (
              <WarningIcon className="warning-icon" />
            )}
          </div>

          {/* Camera Device Dropdown */}
          <CameraDeviceDropdown
            deviceId={videoDeviceId}
            setDeviceId={setVideoDeviceId}
            permissionsGranted={permissions.camera}
          >
            <div className="dropdown-button">
              <CaretDownFilled className="dropdown-icon" />
            </div>
          </CameraDeviceDropdown>
        </div>

        {/* Virtual Background Button */}
        <div
          className={`control-button ${!permissions.camera ? 'disabled' : ''}`}
          onClick={() => {
            if (permissions.camera) {
              setIsVisualEffectsModalOpen(true);
            }
          }}
        >
          <VirtualBackgroundIcon className="control-icon virtual-bg-icon" />
        </div>

        {/* Settings Button */}
        <div
          className="control-button"
          onClick={() => {
            setIsSettingsModalOpen(true);
          }}
        >
          <SettingOutlined className="control-icon" />
        </div>
      </div>

      {/* Audio Video Settings Modal */}
      {/* <AudioVideoSettingsModal
        audio={isAudioModalOpen}
        video={isVideoModalOpen}
        open={isAudioModalOpen || isVideoModalOpen}
        setOpen={isAudioModalOpen ? setIsAudioModalOpen : setIsVideoModalOpen}
        deviceId={isAudioModalOpen ? audioDeviceId : videoDeviceId}
        setDeviceId={isAudioModalOpen ? setAudioDeviceId : setVideoDeviceId}
        track={isAudioModalOpen ? audioTrack : videoTrack}
        setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
        isNoiseCancellationActive={isNoiseCancellationActive}
        setIsNoiseCancellationActive={setIsNoiseCancellationActive}
        isEchoCancellationActive={isEchoCancellationActive}
        setIsEchoCancellationActive={setIsEchoCancellationActive}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      /> */}

      {/* Audio Settings Modal */}
      {/* <Modal
        open={isAudioModalOpen}
        onCancel={() => setIsAudioModalOpen(false)}
        footer={null}
      >
        <AudioSettings
          deviceId={audioDeviceId}
          setDeviceId={setAudioDeviceId}
          track={audioTrack}
          speakerDeviceId={speakerDeviceId}
          setSpeakerDeviceId={setSpeakerDeviceId}
          room={room}
        />
      </Modal> */}

      {/* Video Settings Modal */}
      {/* <Modal
        open={isVideoModalOpen}
        onCancel={() => setIsVideoModalOpen(false)}
        footer={null}
      >
        <VideoSettings
          deviceId={videoDeviceId}
          setDeviceId={setVideoDeviceId}
          track={videoTrack}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
          open={isVideoModalOpen}
          trackFacingMode={trackFacingMode}
        />
      </Modal> */}

      {/* Settings Modal */}
      <SettingsPrejoin
        open={isSettingsModalOpen}
        setOpen={setIsSettingsModalOpen}
        audioDeviceId={audioDeviceId}
        setAudioDeviceId={setAudioDeviceId}
        audioTrack={(() => {
          const audioTrack = tracks?.filter((track) => track.kind === Track.Kind.Audio)[0];
          console.log('🎵 Passing audio track to SettingsPrejoin:', audioTrack);
          return audioTrack;
        })()}
        videoDeviceId={videoDeviceId}
        setVideoDeviceId={setVideoDeviceId}
        videoTrack={videoTrack}
        speakerDeviceId={undefined} // Add speaker device state if needed
        setSpeakerDeviceId={undefined} // Add speaker device setter if needed
        room={room}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
        brightness={brightness}
        onBrightnessChange={handleBrightnessChange}
        permissions={permissions}
      />

      {/* Virtual Background Modal */}
      <VirtualBackgroundModal
        open={isVisualEffectsModalOpen}
        setOpen={setIsVisualEffectsModalOpen}
        backgrounds={backgrounds}
        setBackgrounds={setBackgrounds}
        room={room}
        onEffectSelected={handleEffectSelected}
        videoTrack={videoTrack}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
        currentEffect={currentEffect}
        brightness={brightness}
        onBrightnessChange={handleBrightnessChange}
      />


      {!isMobileBrowser() && !isSafari() && (
        <PermissionUi
          open={showPermissionModal}
          onClose={() => setShowPermissionModal(false)}
          permissionType={permissionType}
          onAllow={async (action) => {
            if (action === 'camera') {
              const success = await requestPermissionsWithBrowserStrategy('camera');
              if (success) {
                // Check if we still need to show the modal for mic
                const perms = await checkPermissionsRealTime();
                if (perms.camera && perms.microphone) {
                  setShowPermissionModal(false);
                }
              } else {
                setShowPermissionModal(true);
              }
            } else if (action === 'mic') {
              const success = await requestPermissionsWithBrowserStrategy('mic');
              if (success) {
                // Check if we still need to show the modal for camera
                const perms = await checkPermissionsRealTime();
                if (perms.camera && perms.microphone) {
                  setShowPermissionModal(false);
                }
              } else {
                setShowPermissionModal(true);
              }
            } else if (action === 'mic_camera') {
              const success = await requestPermissionsWithBrowserStrategy('mic_camera');
              if (success) {
                // If both permissions are granted, close the modal
                setShowPermissionModal(false);
              } else {
                setShowPermissionModal(true);
              }
            } else if (action === 'skip') {
              setShowPermissionModal(false);
            }
          }}
        />
      )}
    </div>
  );
}