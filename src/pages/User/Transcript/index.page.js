/* eslint-disable */

import React, { useState, useEffect, useRef } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useParams, useSearchParams } from "react-router-dom";
import { decoder } from "../../../utils";
import { VideoConferenceService } from "../../../services/User/VideoConference/index.service";
import VideoPlayer from "./components/VideoPlayer";
import TranscriptCard from "./components/TranscriptCard";
import ParticipantCard from "./components/ParticipantCard";
import Sidebar from "./components/Sidebar";
import TabHeader from "./components/TabHeader";
import "./index.page.scss";
import moment from "moment";
import StatusCard from "./components/StatusCard";

const formatTime = (seconds) => {
  if (!seconds) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.round(seconds % 60);

  const timeString = `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;

  return hours > 0
    ? `${String(hours).padStart(2, "0")}:${timeString}`
    : timeString;
};

const LoadingSpinner = () => (
  <div className="loading-container">
    <div className="loading-spinner"></div>
  </div>
);

const ErrorMessage = ({ message }) => (
  <div className="error-container">
    <div>{message}</div>
  </div>
);

function TranscriptionPage() {
  const [activeTab, setActiveTab] = useState('1');
  const [sidebarTab, setSidebarTab] = useState('1');
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [transcriptionData, setTranscriptionData] = useState(null);
  const [participantsData, setParticipantsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [transcriptionStatus, setTranscriptionStatus] = useState(null);
  const [activeMeetingAnalysisPlan, setActiveMeetingAnalysisPlan] = useState(null);
  const [isShared, setIsShared] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [tabsContentHeight, setTabsContentHeight] = useState('calc(100vh - 500px)');
  const isMounted = useRef(true);
  const apiCallInProgress = useRef(false);

  const { meetingId } = useParams();
  const [searchParams] = useSearchParams();
  const recordingId = decoder(searchParams.get("recordingId"));
  const sessionId = decoder(searchParams.get("sessionId"));
  const userId = decoder(searchParams.get("userId"));

  const MAX_RETRIES = 3;
  const RETRY_DELAY = 5000; // 5 seconds

  const videoPlayerRef = useRef(null);

  const fetchData = async (isRetry = false) => {
    if (apiCallInProgress.current && !isRetry) {
      // console.log('API call already in progress, skipping...');
      return;
    }

    apiCallInProgress.current = true;
    const startTime = new Date().getTime();
    // console.log('Starting API calls at:', new Date().toISOString());

    try {
      // console.log('Starting Transcription API call...');
      const transcriptionStartTime = new Date().getTime();

      const transcriptionResponse = await VideoConferenceService.getTranscriptionDataService(
        decoder(meetingId),
        recordingId,
        userId
      );

      const transcriptionEndTime = new Date().getTime();
      // console.log('Transcription API call completed in:', (transcriptionEndTime - transcriptionStartTime) / 1000, 'seconds');

      if (!isMounted.current) {
        // console.log('Component unmounted during API call, aborting...');
        return;
      }

      // console.log('Full Transcription API Response:', transcriptionResponse);
      // console.log('Transcription Status:', transcriptionResponse.data?.status);
      // console.log('Is Shared User:', transcriptionResponse.data?.is_share_user);
      // console.log('Active Meeting Analysis Plan:', transcriptionResponse.data?.active_meeting_analysis_plan);
      // console.log('Video URL:', transcriptionResponse.data?.video_url);

      if (transcriptionResponse.success) {
        setTranscriptionData(transcriptionResponse.data);
        setTranscriptionStatus(transcriptionResponse.data?.status);
        setIsShared(transcriptionResponse.data?.is_share_user !== 0);
        setActiveMeetingAnalysisPlan(
          transcriptionResponse.data?.active_meeting_analysis_plan === 1 ? "ACTIVE" : "INACTIVE"
        );

        // If status is queued and we haven't reached max retries, schedule a retry
        if ((transcriptionResponse.data?.status === "QUEUED" ||
          transcriptionResponse.data?.status === "queued") &&
          retryCount < MAX_RETRIES) {
          // console.log('Status is queued, scheduling retry...');
          setTimeout(() => {
            if (isMounted.current) {
              setRetryCount(prev => prev + 1);
              fetchData(true);
            }
          }, RETRY_DELAY);
        }
      }

      // console.log('Starting Participants API call...');
      const participantsStartTime = new Date().getTime();

      const participantsResponse = await VideoConferenceService.getParticipantsLogsService(
        decoder(meetingId),
        sessionId
      );

      const participantsEndTime = new Date().getTime();
      // console.log('Participants API call completed in:', (participantsEndTime - participantsStartTime) / 1000, 'seconds');

      if (!isMounted.current) {
        // console.log('Component unmounted during API call, aborting...');
        return;
      }

      // console.log('Participants API Response:', participantsResponse);

      if (participantsResponse.success) {
        setParticipantsData(participantsResponse.data);
      }

      const endTime = new Date().getTime();
      // console.log('All API calls completed in:', (endTime - startTime) / 1000, 'seconds');
      // console.log('Finished at:', new Date().toISOString());
    } catch (err) {
      console.error('Error fetching data:', err);
      console.error('Error occurred at:', new Date().toISOString());
      setError(err.message);
    } finally {
      if (isMounted.current) {
        apiCallInProgress.current = false;
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    isMounted.current = true;
    // console.log('useEffect triggered at:', new Date().toISOString());
    // console.log('Meeting ID:', meetingId);
    // console.log('Recording ID:', recordingId);
    // console.log('Session ID:', sessionId);
    // console.log('User ID:', userId);

    fetchData();

    return () => {
      isMounted.current = false;
      // console.log('Component cleanup at:', new Date().toISOString());
    };
  }, [meetingId, recordingId, sessionId, userId]);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
      // Calculate the height for tabs content
      const videoPlayerHeight = 300; // Approximate height of video player
      const tabHeaderHeight = 50; // Approximate height of tab header
      const paddingAndMargins = 50; // Approximate height for padding and margins
      const calculatedHeight = window.innerHeight - (videoPlayerHeight + tabHeaderHeight + paddingAndMargins);
      setTabsContentHeight(`${calculatedHeight}px`);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const handleDownload = () => {
    if (sidebarTab === '1') {
      // Download transcript
      if (!transcriptionData?.transcription?.transcription) return;

      const transcriptData = transcriptionData.transcription.transcription.map((item) => {
        const cleanedTranscript = item?.transcript.replace(/^,/, "");
        return `${item?.start_time} :\n${cleanedTranscript}\n\n`;
      });

      const blob = new Blob([transcriptData.join("")], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `transcript_${decoder(meetingId)}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (sidebarTab === '2') {
      // Download participants
      if (!participantsData?.rows) return;

      const participantData = participantsData.rows.map((participant) => {
        return `Name: ${participant?.screen_name}\nRole: ${participant?.role}\nJoin Time: ${moment(participant?.joined_at).format("HH:mm")}\nLeave Time: ${moment(participant?.leave_at).format("HH:mm")}\n\n`;
      });

      const blob = new Blob([participantData.join("")], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `participants_${decoder(meetingId)}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleSeek = (time) => {
    if (videoPlayerRef.current) {
      videoPlayerRef.current.seekTo(time);
    }
  };

  const handleProgress = (progress) => {
    setCurrentTime(progress.playedSeconds);
  };

  const getEmptyStateMessage = (type) => {
    if (transcriptionStatus === "FAILED" || transcriptionStatus === "failed") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
          <p>📝 We're sorry — the transcription couldn't be completed.</p>
          <p className="text-muted">🔄 Please try again, or 💬 reach out to our team if you'd like assistance.</p>
          </div>
        </div>
      );
    }

    if (transcriptionStatus === "analysis_failed") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
          <p>🧠 Unfortunately, we ran into an issue during the analysis.</p>
        <p className="text-muted">🔁 You're welcome to try again, or 📞 contact support if the problem continues.</p>
          </div>
        </div>
      );
    }

    if (transcriptionStatus === "QUEUED" || transcriptionStatus === "queued") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
            <p>✨ Your meeting is in our magic queue!</p>
            <p className="text-muted">Daakia AI is warming up its neurons to create something special for you.</p>
          </div>
        </div>
      );
    }

    if (transcriptionStatus === "IN_PROCESS" || transcriptionStatus === "analysis_inprogress" || transcriptionStatus === "inprocess") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
            <p>🎯 Daakia AI is crafting your {type} with care...</p>
            <p className="text-muted">We're turning your conversation into golden insights. Just a moment longer!</p>
          </div>
        </div>
      );
    }

    // Different messages for different tabs
    if (type === "key insights") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
          <p>💡 No key insights found for this meeting.</p>
          <p className="text-muted">We'll highlight important moments here once any standout points are identified.</p>
          </div>
        </div>
      );
    }

    if (type === "summary") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
          <p>📝 No summary available for this meeting.</p>
<p className="text-muted">A summary will appear here once we're able to capture the key points from the discussion.</p>
          </div>
        </div>
      );
    }

    if (type === "action items") {
      return (
        <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
          <div className="text-center">
            <p>📋 No action items identified from this meeting.</p>
            <p className="text-muted">We'll list any next steps here when actionable points are detected.</p>
          </div>
        </div>
      );
    }

    // Default message for any other type
    return (
      <div className="empty-state-message d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
        <div className="text-center">
          <p>🌟 Daakia AI is preparing something special...</p>
          <p className="text-muted">Your {type} will be ready before you know it!</p>
        </div>
      </div>
    );
  };

  const mainItems = [
    {
      key: '1',
      label: 'Key Insights / Takeaways',
      children: (
        <div className="p-3">
          {loading ? (
            <LoadingSpinner />
          ) : error ? (
            <ErrorMessage message="Error loading insights" />
          ) : transcriptionData?.takeaways?.length > 0 ? (
            <div style={{ padding: '1rem', border: '1px solid #178fff', borderRadius: '10px' }}>
              <ul style={{ marginBottom: '0', fontSize: '16px', paddingLeft: '20px', listStyleType: 'disc' }}>
                {transcriptionData?.takeaways?.map((insight, index) => (
                  <li key={index} style={{
                    marginBottom: '0.5rem',
                    position: 'relative',
                    paddingRight: '80px'
                  }}>
                    <div style={{ paddingRight: '16px'}}>
                      {insight?.text}
                    </div>
                    <span style={{
                      position: 'absolute',
                      top: '0',
                      right: '0',
                      color: '#666',
                      whiteSpace: 'nowrap'
                    }}>
                      {formatTime(insight?.start_time)}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

          ) : (
            getEmptyStateMessage("key insights")
          )}
        </div>
      )
    },
    {
      key: '2',
      label: 'Summary',
      children: (
        <div className="p-3">
          {loading ? (
            <LoadingSpinner />
          ) : error ? (
            <ErrorMessage message="Error loading summary" />
          ) : transcriptionData?.summary ? (
            <div style={{ padding: '1rem', border: '1px solid #178fff', borderRadius: '4px', borderRadius: '10px' }}>
              <p style={{ fontSize: '16px' }}>
                {transcriptionData?.summary}
              </p>
            </div>
          ) : (
            getEmptyStateMessage("summary")
          )}
        </div>
      )
    },
    {
      key: '3',
      label: 'Action Items',
      children: (
        <div className="p-3">
          {loading ? (
            <LoadingSpinner />
          ) : error ? (
            <ErrorMessage message="Error loading action items" />
          ) : transcriptionData?.action_items?.length > 0 ? (
            <div style={{ padding: '1rem', border: '1px solid #178fff', borderRadius: '4px', borderRadius: '10px' }}>
              <ul className="mb-0 pt-2" style={{ fontSize: '16px', paddingLeft: '20px', listStyleType: 'disc' }}>
                {transcriptionData?.action_items?.map((action, index) => (
                 <li key={index} style={{
                  marginBottom: '0.5rem',
                  position: 'relative',
                  paddingRight: '80px'
                }}>
                  <div style={{ paddingRight: '16px'}}>
                    {action?.text}
                  </div>
                  <span style={{
                    position: 'absolute',
                    top: '0',
                    right: '0',
                    color: '#666',
                    whiteSpace: 'nowrap'
                  }}>
                    {formatTime(action?.start_time)}
                  </span>
                </li>
                ))}
              </ul>
            </div>
          ) : (
            getEmptyStateMessage("action items")
          )}
        </div>
      )
    },
  ];

  const sidebarItems = [
    {
      key: '1',
      label: 'Transcript',
      children: (
        <div className="p-3 h-100">
          {loading ? (
            <LoadingSpinner />
          ) : error ? (
            <ErrorMessage message="Error loading transcript" />
          ) : transcriptionData?.transcription?.transcription?.length > 0 ? (
            <div className="transcript-container" style={{
              padding: '1rem',
              border: '1px solid #178fff',
              borderRadius: '10px',
              height: '100%',
              overflowY: 'auto'
            }}>
              {transcriptionData?.transcription?.transcription?.map((item, index) => {
                const isActive = currentTime >= item?.start_time &&
                  currentTime < (transcriptionData?.transcription?.transcription[index + 1]?.start_time || Infinity);

                return (
                  <TranscriptCard
                    key={index}
                    timestamp={formatTime(item?.start_time)}
                    content={item?.transcript}
                    isActive={isActive}
                    onSeek={() => handleSeek(item?.start_time)}
                  />
                );
              })}
            </div>
          ) : (
            <div className="empty-state-message">
              <p>No transcription available for this meeting.</p>
              <p className="text-muted">Transcription will appear here when available.</p>
            </div>
          )}
        </div>
      ),
    },
    {
      key: '2',
      label: `Participants (${participantsData?.rows?.length || 0})`,
      children: (
        <div className="p-3">
          {loading ? (
            <LoadingSpinner />
          ) : error ? (
            <ErrorMessage message="Error loading participants" />
          ) : (
            participantsData?.rows?.map((participant, index) => (
              <ParticipantCard
                key={index}
                name={participant?.screen_name}
                role={participant?.role}
                joinTime={moment(participant?.joined_at).format("HH:mm")}
                leaveTime={moment(participant?.leave_at).format("HH:mm")}
                index={index}
              />
            ))
          )}
        </div>
      ),
    },
  ];

  const getFilteredItems = () => {
    if (!isSmallScreen) {
      return mainItems.filter(item => !['4', '5'].includes(item.key));
    }
    return mainItems;
  };

  return (
    <div className="transcription-layout vh-100 d-flex flex-column">
      <div className="flex-grow-1 overflow-hidden">
        <div className="container-fluid h-100 p-0">
          <div className="row h-100 m-0">
            <PanelGroup direction="horizontal" className="p-0">
              <Panel defaultSize={70} minSize={30}>
                <div className="left-section d-flex flex-column overflow-hidden h-100 p-0">
                  <VideoPlayer
                    ref={videoPlayerRef}
                    videoUrl={transcriptionData?.video_url}
                    onProgress={handleProgress}
                  />
                  <div className="bottom-container px-2 flex-grow-1 bg-white border-top d-flex flex-column" style={{ height: '100%' }}>
                    <TabHeader
                      items={getFilteredItems()}
                      activeKey={activeTab}
                      onChange={setActiveTab}
                    />
                    <div className="tabs-content" style={{ flex: 1, overflow: 'auto', maxHeight: tabsContentHeight }}>
                      <div className="p-2">
                        {mainItems.map(item => (
                          <div key={item.key} style={{ display: item.key === activeTab ? 'block' : 'none' }}>
                            {item.children}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>

              {!isSmallScreen && (
                <>
                  <PanelResizeHandle className="resize-handle">
                    <div className="resize-handle-content">
                      <div className="resize-handle-dots">
                        <div className="dot" />
                        <div className="dot" />
                        <div className="dot" />
                      </div>
                    </div>
                  </PanelResizeHandle>

                  <Panel defaultSize={50} minSize={20}>
                    {(transcriptionStatus === "QUEUED" ||
                      transcriptionStatus === "queued" ||
                      transcriptionStatus === "IN_PROCESS" ||
                      transcriptionStatus === "analysis_inprogress" ||
                      transcriptionStatus === "inprocess") ? (
                      <div className="status-container">
                        <StatusCard status={
                          isShared
                            ? transcriptionStatus
                            : (activeMeetingAnalysisPlan === "ACTIVE"
                              ? transcriptionStatus
                              : activeMeetingAnalysisPlan)
                        } />
                      </div>
                    ) : (
                      <Sidebar
                        items={sidebarItems}
                        activeTab={sidebarTab}
                        onTabChange={setSidebarTab}
                        onDownload={handleDownload}
                      />
                    )}
                  </Panel>
                </>
              )}
            </PanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TranscriptionPage;